using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MaintenanceController : ControllerBase
    {
        private readonly ILessonService _lessonService;
        private readonly ILogger<MaintenanceController> _logger;

        public MaintenanceController(ILessonService lessonService, ILogger<MaintenanceController> logger)
        {
            _lessonService = lessonService;
            _logger = logger;
        }

        /// <summary>
        /// Manually trigger cleanup of archived lessons older than specified days.
        /// This endpoint is for testing and manual maintenance purposes.
        /// </summary>
        /// <param name="olderThanDays">Number of days to use as cutoff for deletion (default: 30)</param>
        /// <returns>Number of lessons deleted</returns>
        [HttpPost("cleanup-archived-lessons")]
        public async Task<IActionResult> CleanupArchivedLessons([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Manual cleanup requested for lessons older than {Days} days", olderThanDays);
                
                if (olderThanDays < 1)
                {
                    return BadRequest("olderThanDays must be at least 1");
                }

                var deletedCount = await _lessonService.PermanentlyDeleteArchivedLessonsAsync(olderThanDays);
                
                _logger.LogInformation("Manual cleanup completed. Deleted {Count} lessons", deletedCount);
                
                return Ok(new { 
                    message = $"Successfully deleted {deletedCount} archived lessons older than {olderThanDays} days",
                    deletedCount = deletedCount,
                    olderThanDays = olderThanDays
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during manual lesson cleanup");
                return StatusCode(500, new { message = "An error occurred during cleanup", error = ex.Message });
            }
        }

        /// <summary>
        /// Get count of archived lessons that would be deleted by cleanup process.
        /// This endpoint is for testing and monitoring purposes.
        /// </summary>
        /// <param name="olderThanDays">Number of days to use as cutoff (default: 30)</param>
        /// <returns>Count of lessons that would be deleted</returns>
        [HttpGet("archived-lessons-count")]
        public async Task<IActionResult> GetArchivedLessonsCount([FromQuery] int olderThanDays = 30)
        {
            try
            {
                if (olderThanDays < 1)
                {
                    return BadRequest("olderThanDays must be at least 1");
                }

                var count = await _lessonService.GetArchivedLessonsCountAsync(olderThanDays);

                _logger.LogInformation("Found {Count} archived lessons older than {Days} days", count, olderThanDays);

                return Ok(new {
                    message = $"Found {count} archived lessons older than {olderThanDays} days",
                    count = count,
                    olderThanDays = olderThanDays,
                    note = count > 0 ? "These lessons would be deleted by the cleanup process" : "No lessons would be deleted"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archived lessons count");
                return StatusCode(500, new { message = "An error occurred", error = ex.Message });
            }
        }
    }
}
