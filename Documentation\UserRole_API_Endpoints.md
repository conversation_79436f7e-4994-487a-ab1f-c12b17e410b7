# UserRole API Endpoints

This document describes the complete CRUD API endpoints for UserRole management.

## Endpoints

### 1. Get All User Roles
**GET** `/api/user/roles`

**Description:** Retrieves all user roles in the system.

**Authorization:** Required (Bearer token)

**Response:**
```json
[
  {
    "id": 1,
    "description": "Administrator"
  },
  {
    "id": 2,
    "description": "<PERSON>tor"
  },
  {
    "id": 3,
    "description": "Student"
  }
]
```

### 2. Create User Role
**POST** `/api/user/roles`

**Description:** Creates a new user role.

**Authorization:** Required (<PERSON><PERSON> token)

**Request Body:**
```json
{
  "id": 4,
  "description": "Manager"
}
```

**Validation:**
- `description` is required and cannot be empty
- `id` must be unique (not already exist)

**Response:** 201 Created with the created role object

**Error Responses:**
- 400 Bad Request: Invalid input or duplicate ID
- 500 Internal Server Error

### 3. Update User Role
**PUT** `/api/user/roles/{id}`

**Description:** Updates an existing user role.

**Authorization:** Required (<PERSON><PERSON> token)

**Parameters:**
- `id` (path): The ID of the role to update

**Request Body:**
```json
{
  "id": 4,
  "description": "Updated Manager"
}
```

**Validation:**
- Path `id` must match the `id` in request body
- `description` is required and cannot be empty

**Response:** 204 No Content on success

**Error Responses:**
- 400 Bad Request: ID mismatch or invalid input
- 404 Not Found: Role doesn't exist
- 500 Internal Server Error

### 4. Delete User Role
**DELETE** `/api/user/roles/{id}`

**Description:** Deletes a user role.

**Authorization:** Required (Bearer token)

**Parameters:**
- `id` (path): The ID of the role to delete

**Validation:**
- Cannot delete a role that is assigned to users

**Response:** 204 No Content on success

**Error Responses:**
- 400 Bad Request: Role is assigned to users
- 404 Not Found: Role doesn't exist
- 500 Internal Server Error

## Business Rules

1. **Role ID Uniqueness:** Each role must have a unique ID
2. **Description Required:** Role description cannot be null or empty
3. **Referential Integrity:** Cannot delete roles that are assigned to users
4. **System Roles:** Be careful when modifying system roles (1=Administrator, 2=Tutor, 3=Student)

## Client-Side Integration

The Blazor client has corresponding methods in `IUserApiService`:

```csharp
Task<List<UserRole>> GetUserRolesAsync();
Task<UserRole?> CreateUserRoleAsync(UserRole role);
Task<bool> UpdateUserRoleAsync(UserRole role);
Task<bool> DeleteUserRoleAsync(int roleId);
```

## Security

- All endpoints require authentication
- Only administrators should have access to role management
- Consider implementing role-based authorization for these endpoints

## Database Schema

```sql
CREATE TABLE [dbo].[UserRoles](
    [ID] [int] NOT NULL,
    [Description] [nvarchar](50) NULL,
    CONSTRAINT [PK_UserRoles] PRIMARY KEY CLUSTERED ([ID] ASC)
)
```
