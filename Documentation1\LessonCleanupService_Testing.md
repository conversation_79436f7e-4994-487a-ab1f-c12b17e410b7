# Lesson Cleanup Service Testing Guide

## Overview
The `LessonCleanupService` is a background service that automatically deletes archived lessons older than a specified number of days (default: 30 days). This document provides comprehensive testing instructions.

## Configuration
The service can be configured via environment variables (preferred) or `appsettings.json` (fallback):

### Environment Variables (Preferred)
```bash
LESSON_CLEANUP_INTERVAL_HOURS=24    # How often to run cleanup (default: 24 hours)
LESSON_CLEANUP_RETENTION_DAYS=30    # Delete lessons archived longer than this (default: 30 days)
```

### appsettings.json (Fallback)
```json
{
  "LessonCleanup": {
    "IntervalHours": 24,    // How often to run cleanup (default: 24 hours)
    "RetentionDays": 30     // Delete lessons archived longer than this (default: 30 days)
  }
}
```

**Configuration Priority:**
1. Environment variables (highest priority)
2. appsettings.json values
3. Default values (IntervalHours: 24, RetentionDays: 30)

## Testing Scenarios

### 1. Manual Testing via API Endpoint

#### Test Cleanup Functionality
```http
POST /api/maintenance/cleanup-archived-lessons?olderThanDays=30
```

**Expected Response:**
```json
{
  "message": "Successfully deleted X archived lessons older than 30 days",
  "deletedCount": 0,
  "olderThanDays": 30
}
```

#### Test with Different Parameters
```http
POST /api/maintenance/cleanup-archived-lessons?olderThanDays=7
```

### 2. Database Testing

#### Setup Test Data
1. Create some lessons in the database
2. Archive them by setting `IsArchived = 1`
3. Manually update `UpdatedUTC` to be older than 30 days:

```sql
-- Create test archived lessons older than 30 days
UPDATE Lessons 
SET IsArchived = 1, UpdatedUTC = DATEADD(day, -35, GETUTCDATE())
WHERE LessonId IN (SELECT TOP 3 LessonId FROM Lessons);

-- Verify test data
SELECT LessonId, IsArchived, UpdatedUTC, 
       DATEDIFF(day, UpdatedUTC, GETUTCDATE()) as DaysOld
FROM Lessons 
WHERE IsArchived = 1;
```

#### Verify Cleanup
After running cleanup, verify the old archived lessons are deleted:

```sql
-- Check if old archived lessons were deleted
SELECT COUNT(*) as RemainingArchivedLessons
FROM Lessons 
WHERE IsArchived = 1 
AND UpdatedUTC < DATEADD(day, -30, GETUTCDATE());
```

### 3. Background Service Testing

#### Check Service Status
Monitor the application logs to verify the service is running:

```
LessonCleanupService initialized. Will run every 24 hours and delete lessons older than 30 days.
LessonCleanupService started.
Starting lesson cleanup process...
Successfully deleted X archived lessons older than 30 days.
```

#### Test Configuration Changes

**Option 1: Using Environment Variables (Recommended)**
1. Set environment variables for testing:
```bash
# Windows (Command Prompt)
set LESSON_CLEANUP_INTERVAL_HOURS=1
set LESSON_CLEANUP_RETENTION_DAYS=7

# Windows (PowerShell)
$env:LESSON_CLEANUP_INTERVAL_HOURS=1
$env:LESSON_CLEANUP_RETENTION_DAYS=7

# Linux/Mac
export LESSON_CLEANUP_INTERVAL_HOURS=1
export LESSON_CLEANUP_RETENTION_DAYS=7
```

2. Restart the application and monitor logs

**Option 2: Using appsettings.json (Fallback)**
1. Modify `appsettings.json` to run more frequently for testing:
```json
{
  "LessonCleanup": {
    "IntervalHours": 1,     // Run every hour for testing
    "RetentionDays": 7      // Shorter retention for testing
  }
}
```

2. Restart the application and monitor logs

### 4. Error Handling Testing

#### Test Database Connection Issues
1. Temporarily modify connection string to invalid value
2. Verify service logs error and continues running
3. Restore connection string

#### Test Invalid Configuration
1. Set negative values in configuration
2. Verify service handles gracefully

### 5. Performance Testing

#### Test with Large Dataset
1. Create many archived lessons (1000+)
2. Run cleanup and monitor:
   - Execution time
   - Memory usage
   - Database performance

## Monitoring and Logging

### Log Messages to Watch For
- **Startup**: `LessonCleanupService initialized`
- **Execution**: `Starting lesson cleanup process`
- **Success**: `Successfully deleted X archived lessons`
- **No Action**: `No archived lessons older than X days found`
- **Errors**: `Error occurred during lesson cleanup`

### Application Insights (if configured)
Monitor the following metrics:
- Cleanup execution frequency
- Number of lessons deleted per run
- Execution duration
- Error rates

## Troubleshooting

### Service Not Running
1. Check if service is registered in `Program.cs`
2. Verify no startup exceptions in logs
3. Check configuration values are valid

### No Lessons Being Deleted
1. Verify there are archived lessons older than retention period
2. Check database connection
3. Verify SQL query logic with manual testing

### Performance Issues
1. Add database indexes on `IsArchived` and `UpdatedUTC` columns
2. Consider batching deletes for very large datasets
3. Monitor database lock contention

## Production Considerations

### Recommended Settings
- **IntervalHours**: 24 (daily cleanup)
- **RetentionDays**: 30-90 (depending on business requirements)

### Azure App Service Configuration
In Azure App Service, set the environment variables in the Configuration section:

1. Go to Azure Portal → App Service → Configuration → Application settings
2. Add new application settings:
   - Name: `LESSON_CLEANUP_INTERVAL_HOURS`, Value: `24`
   - Name: `LESSON_CLEANUP_RETENTION_DAYS`, Value: `30`
3. Save and restart the app service

### Database Maintenance
Consider adding database indexes:
```sql
CREATE INDEX IX_Lessons_Archived_Updated 
ON Lessons (IsArchived, UpdatedUTC) 
WHERE IsArchived = 1;
```

### Monitoring Alerts
Set up alerts for:
- Service failures
- Unusually high deletion counts
- Long execution times

## Manual Cleanup Commands

### Emergency Cleanup
If immediate cleanup is needed:
```http
POST /api/maintenance/cleanup-archived-lessons?olderThanDays=1
```

### Bulk Historical Cleanup
For initial deployment or data migration:
```http
POST /api/maintenance/cleanup-archived-lessons?olderThanDays=365
```
