using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class LessonCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<LessonCleanupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly TimeSpan _period;
        private readonly int _retentionDays;

        public LessonCleanupService(
            IServiceProvider serviceProvider,
            ILogger<LessonCleanupService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
            
            // Read configuration settings
            var cleanupIntervalHours = Environment.GetEnvironmentVariable("ConnectionStrings_MusicSchool")
                ?? _configuration.GetValue<int>("LessonCleanup:IntervalHours", 24);
            _retentionDays = _configuration.GetValue<int>("LessonCleanup:RetentionDays", 30);
            _period = TimeSpan.FromHours(cleanupIntervalHours);
            
            _logger.LogInformation("LessonCleanupService initialized. Will run every {Hours} hours and delete lessons older than {Days} days.", 
                cleanupIntervalHours, _retentionDays);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LessonCleanupService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformCleanupAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during lesson cleanup.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("LessonCleanupService stopped.");
        }

        private async Task PerformCleanupAsync()
        {
            _logger.LogInformation("Starting lesson cleanup process...");

            using var scope = _serviceProvider.CreateScope();
            var lessonService = scope.ServiceProvider.GetRequiredService<ILessonService>();

            try
            {
                var deletedCount = await lessonService.PermanentlyDeleteArchivedLessonsAsync(_retentionDays);
                
                if (deletedCount > 0)
                {
                    _logger.LogInformation("Successfully deleted {Count} archived lessons older than {Days} days.", 
                        deletedCount, _retentionDays);
                }
                else
                {
                    _logger.LogInformation("No archived lessons older than {Days} days found for cleanup.", _retentionDays);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform lesson cleanup.");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LessonCleanupService is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}
