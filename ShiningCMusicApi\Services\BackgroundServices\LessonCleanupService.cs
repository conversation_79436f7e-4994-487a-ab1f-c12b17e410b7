using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class LessonCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<LessonCleanupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly TimeSpan _period;
        private readonly int _retentionDays;

        public LessonCleanupService(
            IServiceProvider serviceProvider,
            ILogger<LessonCleanupService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;

            // Read configuration settings - check environment variables first, then fall back to appsettings.json
            var cleanupIntervalHours = GetConfigurationValue("LESSON_CLEANUP_INTERVAL_HOURS", "LessonCleanup:IntervalHours", 24);
            _retentionDays = GetConfigurationValue("LESSON_CLEANUP_RETENTION_DAYS", "LessonCleanup:RetentionDays", 30);
            _period = TimeSpan.FromHours(cleanupIntervalHours);

            _logger.LogInformation("LessonCleanupService initialized. Will run every {Hours} hours and delete lessons older than {Days} days.",
                cleanupIntervalHours, _retentionDays);
        }

        private int GetConfigurationValue(string environmentVariableName, string configurationKey, int defaultValue)
        {
            // First try environment variable
            var envValue = Environment.GetEnvironmentVariable(environmentVariableName);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", environmentVariableName, envIntValue);
                return envIntValue;
            }

            // Fall back to configuration
            var configValue = _configuration.GetValue<int>(configurationKey, defaultValue);
            _logger.LogInformation("Using configuration {ConfigKey} = {Value} (default: {Default})",
                configurationKey, configValue, defaultValue);
            return configValue;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LessonCleanupService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformCleanupAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during lesson cleanup.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("LessonCleanupService stopped.");
        }

        private async Task PerformCleanupAsync()
        {
            _logger.LogInformation("Starting lesson cleanup process...");

            using var scope = _serviceProvider.CreateScope();
            var lessonService = scope.ServiceProvider.GetRequiredService<ILessonService>();

            try
            {
                var deletedCount = await lessonService.PermanentlyDeleteArchivedLessonsAsync(_retentionDays);
                
                if (deletedCount > 0)
                {
                    _logger.LogInformation("Successfully deleted {Count} archived lessons older than {Days} days.", 
                        deletedCount, _retentionDays);
                }
                else
                {
                    _logger.LogInformation("No archived lessons older than {Days} days found for cleanup.", _retentionDays);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform lesson cleanup.");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LessonCleanupService is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}
