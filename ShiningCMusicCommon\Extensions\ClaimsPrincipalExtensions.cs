using System.Security.Claims;
using ShiningCMusicCommon.Enums;

namespace ShiningCMusicCommon.Extensions
{
    /// <summary>
    /// Extension methods for ClaimsPrincipal to provide type-safe role checking
    /// </summary>
    public static class ClaimsPrincipalExtensions
    {
        /// <summary>
        /// Checks if the user is in the specified role using the UserRoleEnum
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <param name="role">The role to check</param>
        /// <returns>True if the user is in the specified role</returns>
        public static bool IsInRole(this ClaimsPrincipal user, UserRoleEnum role)
        {
            return user.IsInRole(role.ToString());
        }

        /// <summary>
        /// Gets the user's role as a UserRoleEnum
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <returns>The user's role enum, or null if not found or invalid</returns>
        public static UserRoleEnum? GetUserRole(this ClaimsPrincipal user)
        {
            var roleIdClaim = user.FindFirst("RoleId")?.Value;
            if (int.TryParse(roleIdClaim, out int roleId) && 
                Enum.IsDefined(typeof(UserRoleEnum), roleId))
            {
                return (UserRoleEnum)roleId;
            }
            return null;
        }

        /// <summary>
        /// Gets the user's role ID as an integer
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <returns>The user's role ID, or null if not found</returns>
        public static int? GetUserRoleId(this ClaimsPrincipal user)
        {
            var roleIdClaim = user.FindFirst("RoleId")?.Value;
            if (int.TryParse(roleIdClaim, out int roleId))
            {
                return roleId;
            }
            return null;
        }
    }
}
