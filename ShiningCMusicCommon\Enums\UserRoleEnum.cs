namespace ShiningCMusicCommon.Enums
{
    /// <summary>
    /// Enum representing user roles in the system.
    /// Values correspond to the ID field in the UserRoles database table.
    /// </summary>
    public enum UserRoleEnum
    {
        /// <summary>
        /// Administrator role with full system access
        /// </summary>
        Administrator = 1,
        
        /// <summary>
        /// Tutor role with access to their own lessons and students
        /// </summary>
        Tutor = 2,
        
        /// <summary>
        /// Student role with access to their own lessons only
        /// </summary>
        Student = 3
    }
}
